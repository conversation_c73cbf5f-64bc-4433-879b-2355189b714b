package com.zenyte.game.model.polls;

import com.zenyte.cores.CoresManager;
import com.zenyte.cores.ScheduledExternalizable;
import com.zenyte.game.model.polls.AnsweredPoll.AnsweredPollQuestion;
import com.zenyte.game.model.ui.InterfacePosition;
import com.zenyte.game.packet.PacketDispatcher;
import com.zenyte.game.util.AccessMask;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.game.util.GsonUtil;

import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import mgi.utilities.StringFormatUtil;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;

/**
 * PollManager - handles polling UI and logic
 */
public final class PollManager implements ScheduledExternalizable {
	private static final Logger log = LoggerFactory.getLogger(PollManager.class);
	public static final Int2ObjectOpenHashMap<Poll> map = new Int2ObjectOpenHashMap<>();
	public static final int INTERFACE_ID = 345;
	private static final int SHIFTED_INTERFACE_ID = INTERFACE_ID << 16;
	public static final int HISTORY_INTERFACE_ID = 310;
	public static final int VOTING_INTERFACE_ID = 400;
	private static final int SHIFTED_VOTING_INTERFACE_ID = VOTING_INTERFACE_ID << 16;
	private static final int DESCRIPTION_HEX_COLOUR = 16750623;
	private static final int VOTES_HEX_COLOUR = 16777215;
	private static final int VOTE_COMPONENT_OFFSET = 12;

	private final Player player;
	private Poll poll;
	private int[] votes;
	private final Map<Integer, AnsweredPoll> polls = new HashMap<>();

	public PollManager() {
		this.player = null;
	}

	public PollManager(final Player player) {
		this.player = player;
	}

	private static Path path(Player player) {
		return Path.of("data", "polls", "answers",
				player.getPlayerInformation().getUsername() + ".json");
	}

	public void loadAnsweredPolls() {
		CoresManager.getServiceProvider().executeNow(() -> {
			try {
				final Path file = path(player);
				if (!Files.exists(file)) {
					log.info("No answered polls file for " + player.getPlayerInformation().getUsername());
					return;
				}
				try (final BufferedReader reader = Files.newBufferedReader(file)) {
					final AnsweredPoll[] existing = GsonUtil.getGSON().fromJson(reader, AnsweredPoll[].class);
					if (existing != null) {
						for (AnsweredPoll ap : existing) {
							if (ap != null) {
								this.polls.put(ap.getPollId(), ap);
							}
						}
						log.info("Loaded " + this.polls.size() + " answered polls for " +
								player.getPlayerInformation().getUsername());
					}
				}
			} catch (Exception e) {
				log.error("Failed to load answered polls", e);
			}
		});
	}

	public void saveAnsweredPolls() {
		CoresManager.getServiceProvider().executeNow(() -> {
			try {
				final Collection<AnsweredPoll> answered = this.polls.values();
				final String json = GsonUtil.getGSON().toJson(answered);
				Files.writeString(path(player), json);
				log.info("Saved answered polls for " + player.getPlayerInformation().getUsername());
			} catch (Exception e) {
				log.error("Failed to save answered polls", e);
			}
		});
	}

	public final void open(final Poll poll) {
		this.poll = poll;
		sendLoadingScreen();
		CoresManager.getServiceProvider().executeWithDelay(this::sendPollStatistics, 1);
	}

	private void sendLoadingScreen() {
		player.getInterfaceHandler().sendInterface(InterfacePosition.CENTRAL, INTERFACE_ID);
		final PacketDispatcher dispatcher = player.getPacketDispatcher();
		dispatcher.sendClientScript(603, poll.getTitle(),
				SHIFTED_INTERFACE_ID | 2,
				SHIFTED_INTERFACE_ID | 12,
				SHIFTED_INTERFACE_ID | 11,
				SHIFTED_INTERFACE_ID | 10,
				"Loading...");
		dispatcher.sendClientScript(604, SHIFTED_INTERFACE_ID | 8, SHIFTED_INTERFACE_ID | 9);
		dispatcher.sendClientScript(604, SHIFTED_INTERFACE_ID | 6, SHIFTED_INTERFACE_ID | 7);
		dispatcher.sendClientScript(604, SHIFTED_INTERFACE_ID | 4, SHIFTED_INTERFACE_ID | 5);
		dispatcher.sendComponentText(INTERFACE_ID, 3, "");
		player.getVarManager().sendVar(375, 8);
	}

	private void sendPollStatistics() {
		player.getInterfaceHandler().closeInterface(InterfacePosition.CENTRAL);
		player.getInterfaceHandler().sendInterface(InterfacePosition.CENTRAL, INTERFACE_ID);
		final PacketDispatcher dispatcher = player.getPacketDispatcher();

		dispatcher.sendClientScript(603, poll.getTitle(),
				SHIFTED_INTERFACE_ID | 2,
				SHIFTED_INTERFACE_ID | 12,
				SHIFTED_INTERFACE_ID | 11,
				SHIFTED_INTERFACE_ID | 10,
				"Building...");
		dispatcher.sendComponentVisibility(INTERFACE_ID, SHIFTED_INTERFACE_ID | 10, true);
		dispatcher.sendClientScript(609,
				poll.getDescription() + "|" + poll.getFormattedEndDate(),
				DESCRIPTION_HEX_COLOUR, 0, 495, 495, 12, 5,
				SHIFTED_INTERFACE_ID | 11);

		String hyperlink = poll.getHyperlink();
		if (hyperlink != null) {
			dispatcher.sendClientScript(610, hyperlink, SHIFTED_INTERFACE_ID | 11);
		}

		dispatcher.sendClientScript(609,
				"Votes: " + StringFormatUtil.format(poll.getVotes()),
				VOTES_HEX_COLOUR, 1, 496, 496, 12, 5,
				SHIFTED_INTERFACE_ID | 11);

		final AnsweredPoll answered = polls.get(poll.getPollId());
		final boolean hasVoted = answered != null;
		final PollQuestion[] questions = poll.getQuestions();

		List<Object> list = new ArrayList<>();
		for (int i = 0; i < questions.length; i++) {
			final PollQuestion question = questions[i];
			final String qHyperlink = question.getHyperlink();
			final String message = "Question " + (i + 1) + "|" +
					question.getQuestion() + "|" +
					(qHyperlink == null ? "||" : qHyperlink + "|") +
					question.buildAnswers();
			list.clear();
			list.add(i);
			list.add(-1);
			list.add(message);
			list.add(question.getAnswers().length);
			list.add(question.getAnswer(answered, i));
			for (PollAnswer answer : question.getAnswers()) {
				list.add(answer.getVotes());
			}
			dispatcher.sendClientScript(624, list.toArray());
		}

		dispatcher.sendClientScript(609, DESCRIPTION_HEX_COLOUR, 1, 496, 496, 12, 5, SHIFTED_INTERFACE_ID | 11);
		dispatcher.sendClientScript(618,
				SHIFTED_INTERFACE_ID | 11,
				SHIFTED_INTERFACE_ID | 12,
				SHIFTED_INTERFACE_ID | 9, 1);

		dispatcher.sendClientScript(604,
				SHIFTED_INTERFACE_ID | 8,
				SHIFTED_INTERFACE_ID | 9,
				"History");

		boolean closed = poll.isClosed();
		dispatcher.sendClientScript(604,
				SHIFTED_INTERFACE_ID | 6,
				SHIFTED_INTERFACE_ID | 7,
				closed || (hasVoted && !poll.isAmendable()) ? "" : "Refresh");

		dispatcher.sendClientScript(604,
				SHIFTED_INTERFACE_ID | 4,
				SHIFTED_INTERFACE_ID | 5,
				closed || (hasVoted && !poll.isAmendable()) ? "" :
						hasVoted ? "Amend" : "Vote");

		if (!closed) {
			dispatcher.sendComponentText(INTERFACE_ID, 3,
					hasVoted
							? (poll.isAmendable()
							? "Votes in this poll can be amended."
							: "Votes in this poll cannot be amended.")
							: "You have not yet voted in this poll.");
		} else {
			dispatcher.sendComponentText(INTERFACE_ID, 3, "This poll has closed.");
		}

		dispatcher.sendClientScript(135, SHIFTED_INTERFACE_ID | 3, 495);
	}

	public final void sendHistory() {
		player.getInterfaceHandler().sendInterface(InterfacePosition.CENTRAL, HISTORY_INTERFACE_ID);
		StringBuilder titles = new StringBuilder(), dates = new StringBuilder();
		int size = map.size();
		for (int i = size; i >= 1; i--) {
			Poll p = map.get(i);
			if (p != null) {
				titles.append("<col=df780f>").append(p.getTitle()).append("</col>|");
				dates.append(p.getFormattedPollDates()).append("|");
			}
		}
		player.getPacketDispatcher().sendClientScript(627, map.size(), titles.toString(), dates.toString());
	}

	public final void vote(final boolean clear, final int questionId) {
		if (clear || votes == null) {
			votes = new int[poll.getQuestions().length];
		}
		player.getInterfaceHandler().sendInterface(InterfacePosition.CENTRAL, VOTING_INTERFACE_ID);
		final PacketDispatcher dispatcher = player.getPacketDispatcher();
		dispatcher.sendComponentSettings(VOTING_INTERFACE_ID, 1, 0, 3199, AccessMask.CLICK_OP1);

		dispatcher.sendClientScript(603, poll.getTitle(),
				SHIFTED_VOTING_INTERFACE_ID | 3,
				SHIFTED_VOTING_INTERFACE_ID | 13,
				SHIFTED_VOTING_INTERFACE_ID | 10,
				SHIFTED_VOTING_INTERFACE_ID | 9,
				"Building...");
		dispatcher.sendClientScript(609,
				poll.getDescription() + "|" + poll.getFormattedEndDate(),
				DESCRIPTION_HEX_COLOUR, 0, 495, 495, 12, 5,
				SHIFTED_VOTING_INTERFACE_ID | 10);

		final String hyperlink = poll.getHyperlink();
		if (hyperlink != null) {
			dispatcher.sendClientScript(610, hyperlink, SHIFTED_INTERFACE_ID | 10);
		}

		PollQuestion[] questions = poll.getQuestions();
		for (int i = 0; i < questions.length; i++) {
			PollQuestion question = questions[i];
			String message = "Question " + (i + 1) + "|" +
					question.getQuestion() + "|" +
					(question.getHyperlink() == null ? "||" : question.getHyperlink() + "|") +
					question.getFormattedPollAnswers();
			int len = question.getAnswers().length;
			int offset = len == 2 ? -4 : len > 3 ? ((len - 3) << 2) : 0;

			if (clear) {
				dispatcher.sendClientScript(619, VOTE_COMPONENT_OFFSET + (i * 256) + offset, message, 0);
			} else {
				dispatcher.sendClientScript(620, VOTE_COMPONENT_OFFSET + (i * 256) + offset, votes[i]);
			}
		}

		dispatcher.sendClientScript(618,
				SHIFTED_VOTING_INTERFACE_ID | 10,
				SHIFTED_VOTING_INTERFACE_ID | 13,
				SHIFTED_VOTING_INTERFACE_ID | 9,
				questionId == -1 ? 0 : 1);

		if (questionId == -1) {
			dispatcher.sendComponentText(VOTING_INTERFACE_ID, 4, "Cast your vote.");
		} else {
			dispatcher.sendComponentText(VOTING_INTERFACE_ID, 4,
					"<col=ff0000>Please answer question " + questionId + ".</col>");
		}

		dispatcher.sendClientScript(604,
				SHIFTED_VOTING_INTERFACE_ID | 5,
				SHIFTED_VOTING_INTERFACE_ID | 6,
				"Clear");
		dispatcher.sendClientScript(604,
				SHIFTED_VOTING_INTERFACE_ID | 7,
				SHIFTED_VOTING_INTERFACE_ID | 8,
				"Cancel");
		dispatcher.sendClientScript(604,
				SHIFTED_VOTING_INTERFACE_ID | 11,
				SHIFTED_VOTING_INTERFACE_ID | 12,
				"Vote");
	}

	public final void handleInterface(final int interfaceId, final int componentId, final int slotId) {
		if (interfaceId == INTERFACE_ID) {
			if (componentId == 5) {
				vote(true, -1);
			} else if (componentId == 7) {
				open(poll);
			} else if (componentId == 9) {
				sendHistory();
			}
		} else if (interfaceId == HISTORY_INTERFACE_ID) {
			if (componentId == 4) {
				Poll p = map.get(map.size() - slotId);
				if (p != null) open(p);
			}
		} else if (interfaceId == VOTING_INTERFACE_ID) {
			switch (componentId) {
				case 2 -> {
					int questionId = slotId >> 5;
					int answerId = slotId % 32;
					if (questionId < votes.length) {
						votes[questionId] = 1 << answerId;
						vote(false, -1);
					}
				}
				case 6 -> vote(true, -1);
				case 8 -> open(poll);
				case 12 -> {
					for (int i = 0; i < votes.length; i++) {
						if (votes[i] == 0) {
							vote(false, i + 1);
							return;
						}
					}
					submitVotes();
				}
			}
		}
	}

	private void submitVotes() {
		player.getDialogueManager().start(new Dialogue(player) {
			@Override
			public void buildDialogue() {
				player.getInterfaceHandler().closeInterface(InterfacePosition.CENTRAL);
				plain("Submitting your vote...", false);
				CoresManager.getServiceProvider().executeWithDelay(() -> {
					boolean firstVote = !polls.containsKey(poll.getPollId());
					if (firstVote) {
						poll.setVotes(poll.getVotes() + 1);
					}

					// Handle vote amendments - subtract from old choices, add to new choices
					AnsweredPoll previousAnswers = polls.get(poll.getPollId());
					PollQuestion[] questions = poll.getQuestions();

					for (int i = 0; i < questions.length; i++) {
						if (i >= votes.length) break;
						int newChosenIndex = getChosenAnswerIndex(votes[i]);
						PollQuestion question = questions[i];
						PollAnswer[] answers = question.getAnswers();

						if (newChosenIndex >= 0 && newChosenIndex < answers.length) {
							// If this is an amendment, subtract vote from previous choice
							if (!firstVote && previousAnswers != null && i < previousAnswers.getQuestions().length) {
								String previousChoice = previousAnswers.getQuestions()[i].getAnswer();
								log.info("Amending vote for question {}: '{}' -> '{}'",
										i + 1, previousChoice, answers[newChosenIndex].getChoice());
								// Find and subtract vote from previous answer
								for (int j = 0; j < answers.length; j++) {
									if (answers[j].getChoice().equals(previousChoice)) {
										int oldVotes = answers[j].getVotes();
										answers[j].setVotes(Math.max(0, oldVotes - 1));
										log.info("Subtracted vote from '{}': {} -> {}",
												previousChoice, oldVotes, answers[j].getVotes());
										break;
									}
								}
							}
							// Add vote to new choice
							int oldVotes = answers[newChosenIndex].getVotes();
							answers[newChosenIndex].setVotes(oldVotes + 1);
							log.info("Added vote to '{}': {} -> {}",
									answers[newChosenIndex].getChoice(), oldVotes, answers[newChosenIndex].getVotes());
						}
					}
					AnsweredPoll ap = new AnsweredPoll();
					ap.setPollId(poll.getPollId());
					ap.setSubmitDate(LocalDateTime.now());
					ap.setTitle(poll.getTitle());
					ap.setIpAddress(player.getIP());
					AnsweredPollQuestion[] answeredQs = new AnsweredPollQuestion[questions.length];
					for (int i = 0; i < questions.length && i < votes.length; i++) {
						AnsweredPollQuestion q = new AnsweredPollQuestion();
						q.setQuestion(questions[i].getQuestion());
						int chosenIndex = getChosenAnswerIndex(votes[i]);
						if (chosenIndex >= 0 && chosenIndex < questions[i].getAnswers().length) {
							q.setAnswer(questions[i].getAnswers()[chosenIndex].getChoice());
						}
						answeredQs[i] = q;
					}
					ap.setQuestions(answeredQs);
					polls.put(poll.getPollId(), ap);
					saveAnsweredPolls();
					player.getDialogueManager().next();
				}, 1);
				plain("Thank you for voting.").executeAction(() -> open(poll));
			}
		});
	}

	/**
	 * Converts a bit flag vote value back to the answer index.
	 * Since votes are stored as (1 << answerId), we need to find which bit is set.
	 * @param voteValue the bit flag value
	 * @return the answer index, or -1 if no valid bit is set
	 */
	private int getChosenAnswerIndex(int voteValue) {
		if (voteValue <= 0) {
			return -1;
		}
		// Find the position of the set bit (0-based index)
		return Integer.numberOfTrailingZeros(voteValue);
	}

	@Override
	public Logger getLog() {
		return log;
	}

	@Override
	public int writeInterval() {
		return 5;
	}

	@Override
	public void read(final @NotNull BufferedReader reader) {
		try {
			final Poll[] arr = GsonUtil.getGSON().fromJson(reader, Poll[].class);
			if (arr != null) {
				List<Poll> list = Arrays.asList(arr);
				list.sort(Comparator.comparingInt(Poll::getPollId));
				list.forEach(p -> {
					if (p != null) map.put(p.getPollId(), p);
				});
				log.info("Loaded " + map.size() + " polls into static map.");
			}
		} catch (Exception e) {
			log.error("Error reading polls.json", e);
		}
	}

	@Override
	public void write() {
		try {
			String json = GsonUtil.getGSON().toJson(new ArrayList<>(map.values()));
			out(json);
			log.info("Exported " + map.size() + " polls to JSON.");
		} catch (Exception e) {
			log.error("Error writing polls", e);
		}
	}

	@Override
	public String path() {
		return "data/polls/polls.json";
	}
}