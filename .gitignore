# Gradle
.gradle/
build/
out/
!**/src/**/build/
**/out/
!**/src/**/out/
!**/assets/scripts/out/
!gradle/wrapper/gradle-wrapper.jar

# IntelliJ IDEA
.idea/*
!.idea/gradle.xml
!.idea/misc.xml
!.idea/modules.xml
!.idea/vcs.xml
!.idea/codeStyles/

# Ignore user-specific IntelliJ files
.idea/workspace.xml
.idea/tasks.xml
.idea/misc.xml
.idea/dictionaries
.idea/httpRequests
.idea/shelf/

# OS-specific
.DS_Store
Thumbs.db
.class
.classpath

# Logs
*.log

# Data
data/backups/
data/clans/
data/authenticator/
data/grandexchange/
data/polls/
data/characters/
data/test/
data/gim/groups.json
data/punishments.json
data/lottery.json
data/clans.json
data/well.json
data/invitedplayers.json
data/inferno completions.json
data/contests/launch.json
data/middleman/handled_middleman_trades.json
data/plugins.dat
data/plugins.version.dat
data/models/models.rar
!data/characters/help.json

# Cache
cache/data/cache/
cache/data/cache-staging/
cache/data/cache-179/
cache/data/cache-211/
cache/data/cache-225/
cache/data/dynamic/
cache/data/cache/patches
cache/data/cache-225.zip
cache/assets/osnr/custom_cs2_decompiled/README.md

# Other Project Directories
dumps/

# Misc
Model list.txt
.kotlin/
!jrebel/bin/
run.bat
error.log
/.idea
.idea/misc.xml
