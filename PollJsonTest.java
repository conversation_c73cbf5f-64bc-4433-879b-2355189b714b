import com.zenyte.game.model.polls.Poll;
import com.zenyte.game.util.GsonUtil;

import java.io.FileReader;
import java.io.IOException;

public class PollJsonTest {
    public static void main(String[] args) {
        try {
            FileReader reader = new FileReader("test_poll.json");
            Poll[] polls = GsonUtil.getGSON().fromJson(reader, Poll[].class);
            
            if (polls != null && polls.length > 0) {
                Poll poll = polls[0];
                System.out.println("Poll loaded successfully!");
                System.out.println("Poll ID: " + poll.getPollId());
                System.out.println("Title: " + poll.getTitle());
                System.out.println("Start Date: " + poll.getStartDate());
                System.out.println("End Date: " + poll.getEndDate());
            } else {
                System.out.println("No polls found in JSON");
            }
            
            reader.close();
        } catch (IOException e) {
            System.err.println("IO Error: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("JSON Parsing Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
